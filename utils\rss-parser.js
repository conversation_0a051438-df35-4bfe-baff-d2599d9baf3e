import * as md5Module from './md5.js';

// 生成更可靠的唯一ID
export function generateUniqueId(article, feedUrl, index) {
  // 组合多个字段以确保唯一性
  let idComponents = [];

  // 添加订阅源URL
  if (feedUrl) {
    idComponents.push('feed:' + feedUrl);
  }

  // 添加文章标题
  if (article.title) {
    idComponents.push('title:' + article.title);
  }

  // 添加文章链接
  if (article.link) {
    idComponents.push('link:' + article.link);
  }

  // 添加发布日期
  if (article.pubDate) {
    idComponents.push('date:' + article.pubDate);
  }

  // 如果有GUID，也添加进来
  if (article.guid) {
    idComponents.push('guid:' + article.guid);
  }

  // 如果有作者，也添加进来
  if (article.author) {
    idComponents.push('author:' + article.author);
  }

  // 添加内容摘要的前100个字符（如果有）
  if (article.summary) {
    idComponents.push('summary:' + article.summary.substring(0, 100));
  }

  // 添加内容的前50个字符（如果有）
  if (article.content) {
    idComponents.push('content:' + article.content.substring(0, 50));
  }

  // 总是添加时间戳和随机数，确保唯一性
  idComponents.push('time:' + Date.now().toString());
  idComponents.push('random:' + Math.random().toString(36).substring(2, 15));

  // 组合所有组件并生成MD5哈希
  const combinedString = idComponents.join('::');

  // 检查md5Module的结构，确保正确调用
  const md5Function = md5Module.default || md5Module;
  const uniqueId = md5Function(combinedString);

  return uniqueId;
}

// RSS解析工具函数
export function parseRSSFeed(xmlString) {
  try {
    const articles = [];
    // 检查是否是Atom格式
    const isAtom = xmlString.includes('<feed') && xmlString.includes('</feed>');
    if (isAtom) {
      // 解析Atom格式
      const entryRegex = /<entry>([\s\S]*?)<\/entry>/g;
      let match;
      while ((match = entryRegex.exec(xmlString)) !== null) {
        const entryContent = match[1];
        // console.log('entryContent (Atom):', entryContent);
        const summary = getTextContentByRegex(entryContent, 'summary') || '';
        // Atom的content标签可能包含HTML，也可能直接是文本
        let content = getContentFromAtomEntry(entryContent) || summary;

        const article = {
          title: getTextContentByRegex(entryContent, 'title'),
          link: getLinkFromAtomEntry(entryContent), // Atom link handling
          summary,
          content,
          pubDate: getTextContentByRegex(entryContent, 'published') || getTextContentByRegex(entryContent, 'updated'),
          guid: getTextContentByRegex(entryContent, 'id') || (() => {
            const link = getLinkFromAtomEntry(entryContent);
            const title = getTextContentByRegex(entryContent, 'title');
            if (link && title) {
              const md5Function = md5Module.default || md5Module;
              return link + md5Function(title).substring(0, 15);
            } else if (link) {
              return link;
            } else if (title) {
              const md5Function = md5Module.default || md5Module;
              return md5Function(title).substring(0, 15);
            }
            return 'guid_missing_' + Date.now() + Math.random().toString(36).substring(2, 15); // Ultimate fallback
          })(),
          author: getAuthorFromAtomEntry(entryContent),
          category: getCategoriesFromAtomEntry(entryContent)
        };
        articles.push(article);
      }
    } else {
      // 解析RSS格式
      const itemRegex = /<item>([\s\S]*?)<\/item>/g;
      let match;
      while ((match = itemRegex.exec(xmlString)) !== null) {
        const itemContent = match[1];
        // console.log('itemContent (RSS):', itemContent);

        // 优先content:encoded，其次是description
        let content = getTextContentByRegex(itemContent, 'content:encoded');
        if (!content) {
            content = getTextContentByRegex(itemContent, 'description');
        }
        // summary可以与description相同，或者更短
        let summary = getTextContentByRegex(itemContent, 'description');
        if (!summary && content) { // 如果description为空但content有值，可以尝试从content生成摘要（此处简单设为content）
            summary = content; // 实际应用中可能需要截断或进一步处理
        }


        // RSS: <category>xxx</category>
        const catMatches = [...itemContent.matchAll(/<category[^>]*>([\s\S]*?)<\/category>/gi)];
        const categories = catMatches.map(m => stripCDATA(m[1].trim())); // Ensure CDATA is stripped from categories

        const article = {
          title: getTextContentByRegex(itemContent, 'title'),
          link: getTextContentByRegex(itemContent, 'link'), // RSS link is usually simpler
          summary: summary || '', // Ensure summary is not null
          content: content || summary || '', // Ensure content is not null
          pubDate: getTextContentByRegex(itemContent, 'pubDate'),
          guid: getTextContentByRegex(itemContent, 'guid') || (() => {
            const link = getTextContentByRegex(itemContent, 'link');
            const title = getTextContentByRegex(itemContent, 'title');
            if (link && title) {
              const md5Function = md5Module.default || md5Module;
              return link + md5Function(title).substring(0, 15);
            } else if (link) {
              return link;
            } else if (title) {
              const md5Function = md5Module.default || md5Module;
              return md5Function(title).substring(0, 15);
            }
            return 'guid_missing_' + Date.now() + Math.random().toString(36).substring(2, 15); // Ultimate fallback
          })(),
          author: getTextContentByRegex(itemContent, 'author') || getTextContentByRegex(itemContent, 'dc:creator'),
          category: categories
        };
        articles.push(article);
      }
    }
    return articles;
  } catch (err) {
    console.error('RSS解析错误:', err);
    return [];
  }
}

// 去除 CDATA
function stripCDATA(str) {
  if (!str) return '';
  return str.replace(/<!\[CDATA\[([\s\S]*?)\]\]>/gi, '$1').trim(); // Trim after stripping
}


function getTextContentByRegex(content, tagName) {
  // 处理link标签的特殊情况 - 这部分逻辑在 Atom 中更常见，RSS的link通常直接在<link>标签内
  if (tagName === 'link' && content.includes('<feed')) { // 假设只为Atom的entryContent调用此特殊逻辑
    return getLinkFromAtomEntry(content); // 复用Atom的link提取逻辑
  }

  // 支持多行内容，并尝试处理标签属性
  const regex = new RegExp(`<${tagName}(?:\\s+[^>]*)?>([\\s\\S]*?)<\\/${tagName}>`, 'i');
  const match = content.match(regex);
  if (match && match[1]) {
    let text = match[1].trim();
    text = stripCDATA(text); // 清理CDATA
    // 如果是description, content等字段，可以考虑是否移除HTML
    // 这里我们选择保留HTML
    return text;
  }
  return '';
}

function getLinkFromAtomEntry(entryContent) {
  // 优先 alternate type="text/html"
  let linkRegex = /<link[^>]*rel=['"]alternate['"][^>]*type=['"]text\/html['"][^>]*href=['"]([^'"]+)['"][^>]*>/i;
  let matchAlt = entryContent.match(linkRegex);
  if (matchAlt && matchAlt[1]) return matchAlt[1].trim();

  // 其次 alternate (无type限制)
  linkRegex = /<link[^>]*rel=['"]alternate['"][^>]*href=['"]([^'"]+)['"][^>]*>/i;
  matchAlt = entryContent.match(linkRegex);
  if (matchAlt && matchAlt[1]) return matchAlt[1].trim();

  // 再次，直接匹配href属性，没有rel="alternate"的link标签
  const linkRegex2 = /<link[^>]*href=['"]([^'"]+)['"][^>]*>/i;
  const match = entryContent.match(linkRegex2);
  return match && match[1] ? match[1].trim() : '';
}


function getAuthorFromAtomEntry(entryContent) { // Renamed for clarity
  const authorBlockMatch = entryContent.match(/<author>([\s\S]*?)<\/author>/i);
  if (authorBlockMatch && authorBlockMatch[1]) {
    const authorBlock = authorBlockMatch[1];
    const nameMatch = authorBlock.match(/<name>([\s\S]*?)<\/name>/i);
    if (nameMatch && nameMatch[1]) {
      return stripCDATA(nameMatch[1].trim());
    }
    // 如果没有<name>，但<author>内直接是文本 (不常见，但做兼容)
    const directAuthorText = stripCDATA(authorBlock.trim());
    if (directAuthorText && !directAuthorText.startsWith('<')) {
        return directAuthorText;
    }
  }
  return '';
}

function getContentFromAtomEntry(entryContent) { // Renamed for clarity
  // 优先匹配 <content type="html"> 或 <content type="xhtml"> 或 <content type="text/html">
  // 这些通常包含完整的HTML内容
  const htmlContentMatch = entryContent.match(/<content[^>]*type=['"](?:html|xhtml|text\/html)['"][^>]*>([\s\S]*?)<\/content>/i);
  if (htmlContentMatch && htmlContentMatch[1]) {
    return stripCDATA(htmlContentMatch[1].trim()); // CDATA包裹的HTML
  }

  // 其次匹配没有type或type="text"的content标签
  const textContentMatch = entryContent.match(/<content[^>]*>([\s\S]*?)<\/content>/i);
  if (textContentMatch && textContentMatch[1]) {
      // 解析内容
      let text = stripCDATA(textContentMatch[1].trim());
      return text;
  }

  // Atom中，如果content不可用，summary通常是备选
  const summary = getTextContentByRegex(entryContent, 'summary');
  if (summary) return summary;

  return '';
}


function getCategoriesFromAtomEntry(entryContent) { // Renamed for clarity
  // Atom: <category term="xxx" .../>
  const catMatches = [...entryContent.matchAll(/<category[^>]*term=["']([^"']+)["'][^>]*\/>/gi)];
  if (catMatches.length > 0) {
      return catMatches.map(m => m[1].trim());
  }
  // 也支持 <category ...>label</category> 的形式，虽然不常见于Atom的term
  const catMatchesWithLabel = [...entryContent.matchAll(/<category[^>]*>([\s\S]*?)<\/category>/gi)];
  return catMatchesWithLabel.map(m => stripCDATA(m[1].trim()));

}

// 解析各种格式的日期字符串，确保在iOS上也能正常工作
function parseDate(dateString) {
  if (!dateString) {
    console.warn('日期字符串为空');
    return { success: false, date: new Date(), originalString: dateString };
  }

  // 尝试直接解析（在大多数现代浏览器中可能有效）
  let date = new Date(dateString);

  // 检查日期是否有效
  if (!isNaN(date.getTime())) {
    return { success: true, date: date, originalString: dateString };
  }

  // 处理RFC 822/RFC 2822格式 (如 "Sat, 04 Mar 2023 00:00:00 GMT")
  // 这是RSS中最常见的日期格式
  const rfc822Regex = /^(?:\w+,\s+)?(\d{1,2})\s+(\w+)\s+(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})(?:\s+([+-]\d{4}|\w+))?$/;
  const match = dateString.match(rfc822Regex);

  if (match) {
    const months = {
      'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
      'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
    };

    const day = parseInt(match[1], 10);
    const month = months[match[2].substring(0, 3)] || 0;
    const year = parseInt(match[3], 10);
    const hour = parseInt(match[4], 10);
    const minute = parseInt(match[5], 10);
    const second = parseInt(match[6], 10);

    // 创建日期对象 (使用iOS兼容的格式)
    return {
      success: true,
      date: new Date(year, month, day, hour, minute, second),
      originalString: dateString
    };
  }

  // 处理ISO 8601格式 (如 "2023-03-04T00:00:00Z")
  // 这是Atom feed中常见的日期格式
  const iso8601Regex = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d+)?(?:Z|([+-]\d{2}):(\d{2}))?$/;
  const isoMatch = dateString.match(iso8601Regex);

  if (isoMatch) {
    const year = parseInt(isoMatch[1], 10);
    const month = parseInt(isoMatch[2], 10) - 1; // 月份从0开始
    const day = parseInt(isoMatch[3], 10);
    const hour = parseInt(isoMatch[4], 10);
    const minute = parseInt(isoMatch[5], 10);
    const second = parseInt(isoMatch[6], 10);

    // 创建日期对象 (使用iOS兼容的格式)
    return {
      success: true,
      date: new Date(year, month, day, hour, minute, second),
      originalString: dateString
    };
  }

  // 如果所有解析方法都失败，返回解析失败状态
  console.warn('无法解析日期:', dateString);
  return { success: false, date: new Date(), originalString: dateString };
}

// 日期格式化
export function formatDate(dateString) {
  // 使用我们的健壮解析函数
  const result = parseDate(dateString);

  // 如果解析失败，使用YYYY-MM-DD格式
  if (!result.success) {
    // 尝试从原始字符串中提取一些信息
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    // 返回格式化的日期字符串
    return `${year}-${month}-${day}`;
  }

  const date = result.date;
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 小于1分钟
  if (diff < 60000) {
    return '刚刚';
  }
  // 小于1小时
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`;
  }
  // 小于24小时
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`;
  }
  // 小于30天
  if (diff < 2592000000) { // 30 * 24 * 60 * 60 * 1000
    return `${Math.floor(diff / 86400000)}天前`;
  }

  // 超过30天显示具体日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  // 对于较老的文章，显示日期
  return `${year}-${month}-${day}`;
}