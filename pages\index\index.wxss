/* pages/index/index.wxss */
.container {
  padding: 0;
  min-height: 100vh;
  background: #f7f7f7;
}

.login-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.primary-button {
  background-color: #1976D2;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
}

/* 筛选器样式 */
.filters-container {
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filters-row {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  min-width: 200rpx;
}

.filter-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.picker {
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 16rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 140rpx;
}

.picker .iconfont {
  font-size: 24rpx;
  color: #888;
  margin-left: 8rpx;
}

.refresh-button {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-left: 20rpx;
  transition: all 0.2s;
}

.refresh-button:active {
  background-color: #e0e0e0;
}

.refresh-icon {
  width: 40rpx;
  height: 40rpx;
}

.article-list {
  padding: 0 20rpx;
}

.article-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  margin-bottom: 24rpx;
  padding: 24rpx;
  transition: box-shadow 0.2s;
}

.article-card:active {
  box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.1);
}

.article-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx; /* 给标题和收藏按钮之间留出空间 */
}

.action-button.favorite {
  background: none;
  border: none;
  padding: 0;
  /* font-size and color removed as they were for font icon */
  line-height: 1; /* May still be useful for alignment */
  width: 48rpx; /* Increased tap area slightly */
  height: 48rpx; /* Increased tap area slightly */
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-icon {
  width: 40rpx;
  height: 40rpx;
}

/* Removed: .action-button.favorite .iconfont.icon-star-fill */

.article-meta {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 10rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.meta-dot {
  margin: 0 8rpx;
}

.article-summary {
  color: #555;
  font-size: 28rpx;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-top: 8rpx;
}

/* 加载提示样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.loading-subtext {
  color: #999;
  font-size: 24rpx;
  text-align: center;
}

/* 无内容提示样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-text {
  color: #666;
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.empty-subtext {
  color: #999;
  font-size: 26rpx;
}

.loading-tip {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}

.no-more-tip {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}