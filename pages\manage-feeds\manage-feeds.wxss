.container {
  padding: 20rpx;
}

.add-feed-section {
  background: #ffffff;
  padding: 24rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.category-input {
  display: flex;
  align-items: center;
  margin: 20rpx 0;
}

.picker {
  background: #f5f5f5;
  padding: 16rpx 24rpx;
  border-radius: 4rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #666666;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin: 20rpx 0;
}

.feeds-list {
  background: #ffffff;
  padding: 24rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.feed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.feed-item:last-child {
  border-bottom: none;
}

.feed-info {
  flex: 1;
}

.feed-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.feed-category, .feed-custom-name {
  font-size: 24rpx;
  color: #666666;
  margin-right: 16rpx;
}

.feed-custom-name {
  color: #1890ff;
}

.update-button {
  background: #1890ff;
  color: #ffffff;
  font-size: 24rpx;
  padding: 0 20rpx;
  margin: 0 10rpx 0 0;
}

.delete-button {
  background: #ff5252;
  color: #ffffff;
  font-size: 24rpx;
  padding: 0 20rpx;
  margin: 0;
}

.categories-section {
  background: #ffffff;
  padding: 24rpx;
  border-radius: 8rpx;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.category-item {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  padding: 12rpx 20rpx;
  border-radius: 32rpx;
}

.category-item text {
  font-size: 24rpx;
  color: #666666;
  margin-right: 12rpx;
}

.category-scroll {
  background: #fff;
  white-space: nowrap;
  padding: 20rpx 0 0 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.category-list {
  display: inline-block;
  padding: 0 20rpx;
}

.category-tag {
  display: inline-block;
  padding: 12rpx 32rpx;
  margin-right: 16rpx;
  border-radius: 32rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  transition: all 0.2s;
}

.category-tag.active {
  background: #1890ff;
  color: #fff;
}

.feed-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx;
  transition: box-shadow 0.2s;
}

.feed-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #222;
}

.feed-meta {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 10rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16rpx;
}

.feed-actions {
  margin-top: 8rpx;
}

.feed-url {
  color: #1890ff;
  word-break: break-all;
}

/* 测试按钮样式 */
.test-button {
  background: #52c41a;
  color: #ffffff;
  border: none;
  border-radius: 4rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
  width: 100%;
}

.test-button:active {
  background: #389e0d;
}