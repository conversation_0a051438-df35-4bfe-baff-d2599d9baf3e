import { checkProxyStatus } from './http-proxy';

/**
 * 测试代理服务
 * @returns {Promise<{success: boolean, message: string}>}
 */
export async function testProxyService() {
  try {
    const result = await checkProxyStatus();
    
    if (result.status === 'ok') {
      return {
        success: true,
        message: '代理服务器连接正常',
        responseTime: Date.now()
      };
    } else {
      return {
        success: false,
        message: result.message || '代理服务器返回异常',
        responseTime: Date.now()
      };
    }
  } catch (error) {
    console.error('代理测试捕获到错误:', error);
    return {
      success: false,
      message: error.message || '未知错误',
      responseTime: Date.now()
    };
  }
}

// 导出默认对象
export default {
  testProxyService
};
