// 收藏管理工具
const FAVORITE_STORAGE_KEY = 'favorite_articles'

// 获取所有收藏
export function getAllFavorites() {
  try {
    const favorites = wx.getStorageSync(FAVORITE_STORAGE_KEY) || [];

    // 确保返回的是数组
    if (!Array.isArray(favorites)) {
      return [];
    }

    // 过滤掉无效的收藏项
    return favorites.filter(item => item && item.guid);
  } catch (err) {
    console.error('获取收藏失败:', err);
    return [];
  }
}

// 判断是否已收藏
export function isFavorite(guid) {
  if (!guid) {
    return false;
  }

  const favorites = getAllFavorites();

  // 使用严格相等比较
  return favorites.some(item => item.guid === guid);
}

// 添加收藏
export function addFavorite(article) {
  try {
    if (!article || !article.guid) {
      return;
    }

    const favorites = getAllFavorites();

    // 检查是否已经收藏过相同ID的文章
    if (!favorites.some(item => item.guid === article.guid)) {
      // 添加时间戳，便于排序
      const articleToSave = {
        ...article,
        favoriteTime: Date.now()
      };

      // 添加到收藏列表的开头
      favorites.unshift(articleToSave);
      wx.setStorageSync(FAVORITE_STORAGE_KEY, favorites);
    }
  } catch (err) {
    console.error('添加收藏失败:', err);
  }
}

// 取消收藏
export function removeFavorite(guid) {
  try {
    if (!guid) {
      return;
    }

    let favorites = getAllFavorites();
    favorites = favorites.filter(item => item.guid !== guid);
    wx.setStorageSync(FAVORITE_STORAGE_KEY, favorites);
  } catch (err) {
    console.error('取消收藏失败:', err);
  }
}