App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    feeds: [],
    categories: []
  },

  onLaunch() {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
    }

    // 加载本地存储的订阅源和分类
    const feeds = wx.getStorageSync('feeds')
    const categories = wx.getStorageSync('categories')
    if (feeds) this.globalData.feeds = feeds
    if (categories) this.globalData.categories = categories
  },

  // 检查登录状态
  checkLoginStatus() {
    return new Promise((resolve, reject) => {
      if (this.globalData.isLoggedIn) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  },

  // 登录方法
  login() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          const userInfo = res.userInfo
          this.globalData.userInfo = userInfo
          this.globalData.isLoggedIn = true
          wx.setStorageSync('userInfo', userInfo)
          resolve(userInfo)
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
}) 