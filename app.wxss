/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
}

/* Material Design 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  padding: 24rpx;
}

/* 主色调按钮 */
.primary-button {
  background-color: #1976D2;
  color: #ffffff;
  border: none;
  border-radius: 4rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.primary-button:active {
  background-color: #1565C0;
}

/* 文本样式 */
.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.caption {
  font-size: 24rpx;
  color: #999999;
}

/* 输入框样式 */
.input {
  background: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 4rpx;
  padding: 16rpx;
  margin: 16rpx 0;
  font-size: 28rpx;
}

/* 分类标签样式 */
.category-tag {
  display: inline-block;
  padding: 8rpx 24rpx;
  background: #E3F2FD;
  color: #1976D2;
  border-radius: 32rpx;
  font-size: 24rpx;
  margin: 8rpx;
}

.category-tag.active {
  background: #1976D2;
  color: #ffffff;
}

/* iconfont 五角星样式 */
@font-face {
  font-family: 'iconfont';
  src: url('https://at.alicdn.com/t/c/font_4398572_1v6k8k8k8k8.woff2') format('woff2'),
       url('https://at.alicdn.com/t/c/font_4398572_1v6k8k8k8k8.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}
.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-decoration: none;
  text-align: center;
  text-transform: none;
  line-height: 1;
  display: inline-block;
}
.icon-star:before {
  content: "\e601";
}
.icon-star-fill:before {
  content: "\e600";
} 