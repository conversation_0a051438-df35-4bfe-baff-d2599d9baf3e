/*
 * [js-md5]{@link https://github.com/emn178/js-md5}
 *
 * @version 0.7.3
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> [<EMAIL>]
 * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2014-2018
 * @license MIT
 */
/*jslint bitwise: true */
(function () {
  'use strict';

  var ERROR = 'input is invalid type';
  var WINDOW = typeof window === 'object';
  var root = WINDOW ? window : {};
  if (root.JS_MD5_NO_WINDOW) {
    WINDOW = false;
  }
  var WEB_WORKER = !WINDOW && typeof self === 'object';
  var NODE_JS = !root.JS_MD5_NO_NODE_JS && typeof process === 'object' && process.versions && process.versions.node;
  if (NODE_JS) {
    root = global;
  } else if (WEB_WORKER) {
    root = self;
  }
  var COMMON_JS = !root.JS_MD5_NO_COMMON_JS && typeof module === 'object' && module.exports;
  var AMD = typeof define === 'function' && define.amd;
  var ARRAY_BUFFER = !root.JS_MD5_NO_ARRAY_BUFFER && typeof ArrayBuffer !== 'undefined';
  var HEX_CHARS = '0123456789abcdef'.split('');
  var EXTRA = [128, 32768, 8388608, -**********];
  var SHIFT = [0, 8, 16, 24];
  var OUTPUT_TYPES = ['hex', 'array', 'digest', 'buffer', 'arrayBuffer', 'base64'];
  var BASE64_ENCODE_CHAR = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'.split('');

  var blocks = [], buffer8;
  if (ARRAY_BUFFER) {
    var buffer = new ArrayBuffer(68);
    buffer8 = new Uint8Array(buffer);
    blocks = new Uint32Array(buffer, 0, 16);
  }

  var md5 = function (message, options) {
    if (typeof message === 'string') {
      return md5.hex(message, options);
    }
    if (message === null || message === undefined) {
      throw ERROR;
    }
    options = options || {};
    var digestbytes = md5.digest(message, options);
    if (options.asBytes) {
      return digestbytes;
    }
    if (options.asString) {
      var output = '', i;
      for (i = 0; i < digestbytes.length; ++i) {
        output += String.fromCharCode(digestbytes[i]);
      }
      return output;
    }
    return md5.hex(digestbytes);
  };

  md5.prototype = {};

  md5.hex = function (message, options) {
    var digest = md5.digest(message, options);
    var result = '', i;
    for (i = 0; i < digest.length; ++i) {
      result += HEX_CHARS[(digest[i] >>> 4) & 0x0F] + HEX_CHARS[digest[i] & 0x0F];
    }
    return result;
  };

  md5.digest = function (message, options) {
    if (message === null || message === undefined) {
      throw ERROR;
    }
    options = options || {};
    var sharedMemory = options.sharedMemory;
    if (typeof message === 'string') {
      message = utf8Encode(message);
    }
    var messageLength = message.length, i, appendBits, offset, bits, quantity;
    var a = 0x67452301, b = 0xefcdab89, c = 0x98badcfe, d = 0x10325476;

    for (i = 0; i < 16; ++i) {
      blocks[i] = 0;
    }

    if (sharedMemory) {
      blocks = new Uint32Array(message.buffer, message.byteOffset, Math.floor(messageLength / 4));
      offset = messageLength % 4;
      appendBits = messageLength * 8;
      if (offset) {
        buffer8.set(new Uint8Array(message.buffer, message.byteOffset + messageLength - offset, offset));
      }
    } else {
      for (i = 0; i < messageLength; ++i) {
        blocks[i >>> 2] |= message[i] << SHIFT[i % 4];
      }
      offset = messageLength % 4;
      appendBits = messageLength * 8;
    }

    if (offset) {
      blocks[messageLength >>> 2] |= EXTRA[offset - 1];
    } else {
      blocks[messageLength >>> 2] = 0x80;
    }

    if (messageLength > 55) {
      for (i = 0; i < 16; ++i) {
        a = md5Block(a, b, c, d, blocks[i * 16], blocks[i * 16 + 1], blocks[i * 16 + 2], blocks[i * 16 + 3],
          blocks[i * 16 + 4], blocks[i * 16 + 5], blocks[i * 16 + 6], blocks[i * 16 + 7],
          blocks[i * 16 + 8], blocks[i * 16 + 9], blocks[i * 16 + 10], blocks[i * 16 + 11],
          blocks[i * 16 + 12], blocks[i * 16 + 13], blocks[i * 16 + 14], blocks[i * 16 + 15], a, b, c, d);
      }
      for (i = 0; i < 16; ++i) {
        blocks[i] = 0;
      }
    }
    blocks[14] = appendBits;
    a = md5Block(a, b, c, d, blocks[0], blocks[1], blocks[2], blocks[3],
      blocks[4], blocks[5], blocks[6], blocks[7],
      blocks[8], blocks[9], blocks[10], blocks[11],
      blocks[12], blocks[13], blocks[14], blocks[15], a, b, c, d);

    var result = [];
    result[0] = a;
    result[1] = b;
    result[2] = c;
    result[3] = d;
    return result;
  };

  md5.array = md5.digest;

  md5.arrayBuffer = function (message, options) {
    if (message === null || message === undefined) {
      throw ERROR;
    }
    options = options || {};
    var digest = md5.digest(message, options);
    var buffer = new ArrayBuffer(16);
    var view = new DataView(buffer);
    view.setInt32(0, digest[0], true);
    view.setInt32(4, digest[1], true);
    view.setInt32(8, digest[2], true);
    view.setInt32(12, digest[3], true);
    return buffer;
  };

  md5.buffer = md5.arrayBuffer;

  md5.base64 = function (message, options) {
    var digest = md5.digest(message, options);
    var result = '', i, a, b, c;
    for (i = 0; i < 15; i += 3) {
      a = digest[i >>> 2] >>> SHIFT[i % 4];
      b = digest[(i + 1) >>> 2] >>> SHIFT[(i + 1) % 4];
      c = digest[(i + 2) >>> 2] >>> SHIFT[(i + 2) % 4];
      result += BASE64_ENCODE_CHAR[a >>> 2] +
        BASE64_ENCODE_CHAR[((a << 4) | (b >>> 4)) & 0x3F] +
        BASE64_ENCODE_CHAR[((b << 2) | (c >>> 6)) & 0x3F] +
        BASE64_ENCODE_CHAR[c & 0x3F];
    }
    a = digest[15 >>> 2] >>> SHIFT[15 % 4];
    result += BASE64_ENCODE_CHAR[a >>> 2] + BASE64_ENCODE_CHAR[(a << 4) & 0x3F] + '==';
    return result;
  };

  var utf8Encode = function (str) {
    var result = [], i, c;
    for (i = 0; i < str.length; ++i) {
      c = str.charCodeAt(i);
      if (c < 0x80) {
        result.push(c);
      } else if (c < 0x800) {
        result.push(0xC0 | (c >> 6));
        result.push(0x80 | (c & 0x3F));
      } else if (c < 0xD800 || c >= 0xE000) {
        result.push(0xE0 | (c >> 12));
        result.push(0x80 | ((c >> 6) & 0x3F));
        result.push(0x80 | (c & 0x3F));
      } else {
        c = 0x10000 + (((c & 0x3FF) << 10) | (str.charCodeAt(++i) & 0x3FF));
        result.push(0xF0 | (c >> 18));
        result.push(0x80 | ((c >> 12) & 0x3F));
        result.push(0x80 | ((c >> 6) & 0x3F));
        result.push(0x80 | (c & 0x3F));
      }
    }
    return result;
  };

  var md5Block = function (a, b, c, d, x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13, x14, x15, aa, bb, cc, dd) {
    var FF = function (a, b, c, d, x, s, t) {
      return (a + (b & c | ~b & d) + x + t) << s | (a + (b & c | ~b & d) + x + t) >>> (32 - s);
    };
    var GG = function (a, b, c, d, x, s, t) {
      return (a + (b & d | c & ~d) + x + t) << s | (a + (b & d | c & ~d) + x + t) >>> (32 - s);
    };
    var HH = function (a, b, c, d, x, s, t) {
      return (a + (b ^ c ^ d) + x + t) << s | (a + (b ^ c ^ d) + x + t) >>> (32 - s);
    };
    var II = function (a, b, c, d, x, s, t) {
      return (a + (c ^ (b | ~d)) + x + t) << s | (a + (c ^ (b | ~d)) + x + t) >>> (32 - s);
    };

    a = FF(a, b, c, d, x0, 7, -680876936);
    d = FF(d, a, b, c, x1, 12, -389564586);
    c = FF(c, d, a, b, x2, 17, 606105819);
    b = FF(b, c, d, a, x3, 22, -1044525330);
    a = FF(a, b, c, d, x4, 7, -176418897);
    d = FF(d, a, b, c, x5, 12, 1200080426);
    c = FF(c, d, a, b, x6, 17, -1473231341);
    b = FF(b, c, d, a, x7, 22, -45705983);
    a = FF(a, b, c, d, x8, 7, 1770035416);
    d = FF(d, a, b, c, x9, 12, -1958414417);
    c = FF(c, d, a, b, x10, 17, -42063);
    b = FF(b, c, d, a, x11, 22, -1990404162);
    a = FF(a, b, c, d, x12, 7, 1804603682);
    d = FF(d, a, b, c, x13, 12, -40341101);
    c = FF(c, d, a, b, x14, 17, -1502002290);
    b = FF(b, c, d, a, x15, 22, 1236535329);

    a = GG(a, b, c, d, x1, 5, -165796510);
    d = GG(d, a, b, c, x6, 9, -1069501632);
    c = GG(c, d, a, b, x11, 14, 643717713);
    b = GG(b, c, d, a, x0, 20, -373897302);
    a = GG(a, b, c, d, x5, 5, -701558691);
    d = GG(d, a, b, c, x10, 9, 38016083);
    c = GG(c, d, a, b, x15, 14, -660478335);
    b = GG(b, c, d, a, x4, 20, -405537848);
    a = GG(a, b, c, d, x9, 5, 568446438);
    d = GG(d, a, b, c, x14, 9, -1019803690);
    c = GG(c, d, a, b, x3, 14, -187363961);
    b = GG(b, c, d, a, x8, 20, 1163531501);
    a = GG(a, b, c, d, x13, 5, -1444681467);
    d = GG(d, a, b, c, x2, 9, -51403784);
    c = GG(c, d, a, b, x7, 14, 1735328473);
    b = GG(b, c, d, a, x12, 20, -1926607734);

    a = HH(a, b, c, d, x5, 4, -378558);
    d = HH(d, a, b, c, x8, 11, -2022574463);
    c = HH(c, d, a, b, x11, 16, 1839030562);
    b = HH(b, c, d, a, x14, 23, -35309556);
    a = HH(a, b, c, d, x1, 4, -1530992060);
    d = HH(d, a, b, c, x4, 11, 1272893353);
    c = HH(c, d, a, b, x7, 16, -155497632);
    b = HH(b, c, d, a, x10, 23, -1094730640);
    a = HH(a, b, c, d, x13, 4, 681279174);
    d = HH(d, a, b, c, x0, 11, -358537222);
    c = HH(c, d, a, b, x3, 16, -722521979);
    b = HH(b, c, d, a, x6, 23, 76029189);
    a = HH(a, b, c, d, x9, 4, -640364487);
    d = HH(d, a, b, c, x12, 11, -421815835);
    c = HH(c, d, a, b, x15, 16, 530742520);
    b = HH(b, c, d, a, x2, 23, -995338651);

    a = II(a, b, c, d, x0, 6, -198630844);
    d = II(d, a, b, c, x7, 10, 1126891415);
    c = II(c, d, a, b, x14, 15, -1416354905);
    b = II(b, c, d, a, x5, 21, -57434055);
    a = II(a, b, c, d, x12, 6, 1700485571);
    d = II(d, a, b, c, x3, 10, -1894986606);
    c = II(c, d, a, b, x10, 15, -1051523);
    b = II(b, c, d, a, x1, 21, -2054922799);
    a = II(a, b, c, d, x8, 6, 1873313359);
    d = II(d, a, b, c, x15, 10, -30611744);
    c = II(c, d, a, b, x6, 15, -1560198380);
    b = II(b, c, d, a, x13, 21, 1309151649);
    a = II(a, b, c, d, x4, 6, -145523070);
    d = II(d, a, b, c, x11, 10, -1120210379);
    c = II(c, d, a, b, x2, 15, 718787259);
    b = II(b, c, d, a, x9, 21, -343485551);

    a = (a + aa) | 0;
    b = (b + bb) | 0;
    c = (c + cc) | 0;
    d = (d + dd) | 0;
    return [a, b, c, d];
  };

  if (COMMON_JS) {
    module.exports = md5;
  } else {
    root.md5 = md5;
    if (AMD) {
      define(function () {
        return md5;
      });
    }
  }
}());