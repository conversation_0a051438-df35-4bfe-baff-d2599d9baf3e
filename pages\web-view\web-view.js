// pages/web-view/web-view.js
Page({
  data: {
    url: '',
    title: '网页浏览'
  },

  onLoad(options) {
    if (options.url) {
      let url = decodeURIComponent(options.url);

      // 解码常见的HTML实体
      url = url.replace(/&amp;/g, '&')
               .replace(/&lt;/g, '<')
               .replace(/&gt;/g, '>')
               .replace(/&quot;/g, '"')
               .replace(/&#x27;/g, "'")
               .replace(/&#x2F;/g, '/')
               .replace(/&#(\d+);/g, (_, dec) => String.fromCharCode(dec));

      // 如果链接看起来不像URL，尝试进一步修复
      if (!url.match(/^https?:\/\//i)) {
        // 如果链接以//开头，添加https:
        if (url.startsWith('//')) {
          url = 'https:' + url;
        }
        // 如果链接不包含协议，添加https://
        else if (!url.includes('://')) {
          url = 'https://' + url;
        }
      }

      console.log('打开URL:', url);

      this.setData({
        url,
        title: options.title || '网页浏览'
      });

      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: this.data.title
      });
    } else {
      wx.showToast({
        title: '无效的URL',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 处理web-view加载错误
  handleError(e) {
    console.error('web-view加载错误:', e.detail);
    wx.showModal({
      title: '加载失败',
      content: '无法打开该网页，是否复制链接到剪贴板？',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: this.data.url,
            success: () => {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              });
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        } else {
          wx.navigateBack();
        }
      }
    });
  }
})
