const app = getApp()

Page({
  data: {
    newFeedUrl: '',
    newCategory: '',
    feeds: [],
    categories: [],
    currentCategory: 'all'
  },

  onLoad() {
    this.loadFeeds()
    this.loadCategories()
  },

  onShow() {
    // 每次显示页面时重新加载数据
    this.loadFeeds()
    this.loadCategories()
  },

  loadFeeds() {
    const feeds = app.globalData.feeds
    this.setData({ feeds })
  },

  loadCategories() {
    const categories = app.globalData.categories
    this.setData({ categories })
  },

  onFeedUrlInput(e) {
    this.setData({
      newFeedUrl: e.detail.value
    })
  },

  onCategoryInput(e) {
    this.setData({
      newCategory: e.detail.value
    })
  },

  onCategorySelect(e) {
    const index = e.detail.value
    this.setData({
      newCategory: this.data.categories[index]
    })
  },

  isValidUrl(urlString) {
    if (typeof urlString !== 'string' || urlString.trim() === '') {
      return false;
    }
    try {
      const url = new URL(urlString);
      // Ensure protocol is http or https
      return url.protocol === "http:" || url.protocol === "https:";
    } catch (e) {
      // If URL constructor fails, it's not a valid URL
      return false;
    }
  },

  async addFeed() {
    let { newFeedUrl, newCategory } = this.data;
    newFeedUrl = newFeedUrl.trim(); // Trim whitespace
    
  

    // 将HTTP转换为HTTPS
    if (newFeedUrl.startsWith('http://')) {
      newFeedUrl = newFeedUrl.replace('http://', 'https://');
    }
    
    // 检查是否已存在相同的URL
    if (this.data.feeds.some(feed => feed.url === newFeedUrl)) {
      wx.showToast({
        title: '该订阅源已存在',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '正在验证加载订阅源...',
    })

    try {
      // 验证RSS源是否有效
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: newFeedUrl,
          method: 'GET',
          header: {
            'Accept': 'text/html,application/xhtml+xml,application/xml;'
          },
          success: (res) => {
            resolve(res)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })

    
      // 检查响应状态码
      if (response.statusCode !== 200) {
        throw new Error(`Invalid response status: ${response.statusCode}`)
      }

      // 检查响应内容类型
      const contentType = response.header['content-type'] || ''
      const isXml = contentType.includes('xml') || 
                    response.data.includes('<?xml') || 
                    response.data.includes('<rss') ||
                    response.data.includes('<feed')

      if (!isXml) {
        throw new Error('Invalid RSS feed format')
      }

      // 解析RSS标题
      let title = newFeedUrl
      try {
        // 使用正则表达式提取标题
        const titleMatch = response.data.match(/<title[^>]*>([^<]+)<\/title>/i)
        if (titleMatch && titleMatch[1]) {
          title = titleMatch[1].trim()
        }
      } catch (parseError) {
        console.error('解析RSS标题失败:', parseError)
      }

      // 添加新订阅源
      const newFeed = {
        url: newFeedUrl,
        title: title,
        category: newCategory
      }

      // 更新全局数据
      const feeds = [...app.globalData.feeds, newFeed]
      app.globalData.feeds = feeds
      wx.setStorageSync('feeds', feeds)

      // 如果是新分类，添加到分类列表
      if (!this.data.categories.includes(newCategory)) {
        const categories = [...this.data.categories, newCategory]
        app.globalData.categories = categories
        wx.setStorageSync('categories', categories)
      }

      // 更新页面数据
      this.setData({
        feeds,
        categories: app.globalData.categories,
        newFeedUrl: '',
        newCategory: ''
      })

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      })
    } catch (err) {
      console.error('添加订阅源失败:', err)
      wx.showToast({
        title: '添加失败，请检查URL',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  deleteFeed(e) {
    const url = e.currentTarget.dataset.url
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订阅源吗？',
      success: (res) => {
        if (res.confirm) {
          const feeds = this.data.feeds.filter(feed => feed.url !== url)
          app.globalData.feeds = feeds
          wx.setStorageSync('feeds', feeds)
          this.setData({ feeds })
        }
      }
    })
  },

  deleteCategory(e) {
    const category = e.currentTarget.dataset.category
    
    // 检查该分类下是否还有订阅源
    const hasFeeds = this.data.feeds.some(feed => feed.category === category)
    if (hasFeeds) {
      wx.showToast({
        title: '请先删除该分类下的订阅源',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个分类吗？',
      success: (res) => {
        if (res.confirm) {
          const categories = this.data.categories.filter(cat => cat !== category)
          app.globalData.categories = categories
          wx.setStorageSync('categories', categories)
          this.setData({ categories })
        }
      }
    })
  },

  isValidUrl(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  // 分类筛选
  switchCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ currentCategory: category })
  }
})

onUpdateFeed(e) {
  const url = e.currentTarget.dataset.url;
  wx.showLoading({ title: '正在更新...' });
  wx.request({
    url,
    method: 'GET',
    success: (res) => {
      try {
        // 解析RSS内容（假设 parseRSSFeed 已在 utils/rss-parser.js 实现并可用）
        const { parseRSSFeed } = require('../../utils/rss-parser');
        const articles = parseRSSFeed(res.data);
        // 存储到本地缓存，key 以 feed url 作为唯一标识
        wx.setStorageSync('feed_articles_' + url, articles);
        wx.showToast({ title: '更新成功', icon: 'success' });
      } catch (err) {
        wx.showToast({ title: '解析失败', icon: 'none' });
      }
    },
    fail: () => {
      wx.showToast({ title: '请求失败', icon: 'none' });
    },
    complete: () => {
      wx.hideLoading();
    }
  });
},