// RSS订阅管理工具
import { requestRSSViaProxy } from './http-proxy'
import { parseRSSFeed } from './rss-parser'

const FEED_STORAGE_KEY = 'rss_feeds'

// 标准化URL，便于比较
function normalizeUrl(url) {
  try {
    if (!url) return '';

    // 确保URL有协议前缀
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    // 使用URL对象标准化URL
    const urlObj = new URL(url);

    // 移除URL末尾的斜杠
    let normalizedUrl = urlObj.origin + urlObj.pathname.replace(/\/$/, '');

    // 添加查询参数（如果有）
    if (urlObj.search) {
      normalizedUrl += urlObj.search;
    }

    return normalizedUrl.toLowerCase();
  } catch (e) {
    // 如果解析失败，返回原始URL
    return url.toLowerCase();
  }
}

// 创建iOS兼容的ISO日期字符串
function createISODateString() {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}T${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}Z`;
}

// 获取所有订阅
export function getAllFeeds() {
  try {
    const feeds = wx.getStorageSync(FEED_STORAGE_KEY) || []
    return feeds
  } catch (err) {
    console.error('获取订阅列表失败:', err)
    return []
  }
}

// 添加新订阅
export function addFeed(feed) {
  try {
    const feeds = getAllFeeds()
    // 标准化URL，便于比较
    const normalizedNewUrl = normalizeUrl(feed.url);

    // 检查是否已存在相同URL的订阅（使用标准化后的URL进行比较）
    const existingFeed = feeds.find(f => normalizeUrl(f.url) === normalizedNewUrl);
    if (existingFeed) {
      throw new Error('您已订阅该订阅源~')
    }

    const isoDateString = createISODateString();

    const newFeed = {
      id: Date.now().toString(), // 使用时间戳作为唯一ID
      url: feed.url,
      title: feed.title || '',
      description: feed.description || '',
      lastUpdated: isoDateString,
      createdAt: isoDateString
    }

    feeds.push(newFeed)
    wx.setStorageSync(FEED_STORAGE_KEY, feeds)
    return newFeed
  } catch (err) {
    console.error('添加订阅失败:', err)
    throw err
  }
}

// 更新订阅信息
export function updateFeed(feedId, updates) {
  try {
    const feeds = getAllFeeds()
    const index = feeds.findIndex(f => f.id === feedId)
    if (index === -1) {
      throw new Error('订阅不存在')
    }

    feeds[index] = {
      ...feeds[index],
      ...updates,
      lastUpdated: createISODateString()
    }

    wx.setStorageSync(FEED_STORAGE_KEY, feeds)
    return feeds[index]
  } catch (err) {
    console.error('更新订阅失败:', err)
    throw err
  }
}

// 删除订阅
export function deleteFeed(feedId) {
  try {
    const feeds = getAllFeeds()
    const newFeeds = feeds.filter(f => f.id !== feedId)
    if (newFeeds.length === feeds.length) {
      throw new Error('订阅不存在')
    }

    wx.setStorageSync(FEED_STORAGE_KEY, newFeeds)
    return true
  } catch (err) {
    console.error('删除订阅失败:', err)
    throw err
  }
}

// 更新订阅内容
export async function updateFeedContent(feedId) {
  try {
    const feeds = getAllFeeds()
    const feed = feeds.find(f => f.id === feedId)
    if (!feed) {
      throw new Error('订阅不存在')
    }

    // 通过代理获取RSS内容
    const response = await requestRSSViaProxy(feed.url)

    if (response.statusCode !== 200) {
      throw new Error('获取RSS内容失败')
    }

    // 解析RSS内容
    const articles = parseRSSFeed(response.data)

    // 更新订阅信息
    const updatedFeed = {
      ...feed,
      lastUpdated: createISODateString(),
      lastArticles: articles.slice(0, 10) // 只保存最新的10篇文章
    }

    return updateFeed(feedId, updatedFeed)
  } catch (err) {
    console.error('更新订阅内容失败:', err)
    throw err
  }
}

// 批量更新所有订阅
export async function updateAllFeeds() {
  try {
    const feeds = getAllFeeds()
    const updatePromises = feeds.map(feed => updateFeedContent(feed.id))
    await Promise.all(updatePromises)
    return true
  } catch (err) {
    console.error('批量更新订阅失败:', err)
    throw err
  }
}