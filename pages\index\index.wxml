<view class="container">
  <!-- 登录状态 -->
  <view class="login-section" wx:if="{{!isLoggedIn}}">
    <button class="primary-button" bindtap="handleLogin">微信授权登录</button>
  </view>

  <!-- 用户信息 -->
  <view class="user-info" wx:if="{{isLoggedIn}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>

  <!-- 筛选器和刷新按钮 -->
  <view class="filters-container">
    <view class="filters-row">
      <!-- 分类选择器 -->
      <view class="filter-item">
        <text class="filter-label">分类:</text>
        <picker bindchange="onCategoryChange" value="{{categoryIndex}}" range="{{categoryRange}}">
          <view class="picker">
            {{categoryRange[categoryIndex]}}
            <text class="iconfont icon-arrow-down"></text>
          </view>
        </picker>
      </view>

      <!-- 订阅源选择器 -->
      <view class="filter-item">
        <text class="filter-label">订阅源:</text>
        <picker bindchange="onFeedChange" value="{{feedIndex}}" range="{{feedRange}}" range-key="{{feedRangeKey}}">
          <view class="picker">
            {{feedRange[feedIndex][feedRangeKey]}}
            <text class="iconfont icon-arrow-down"></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 刷新按钮 -->
    <view class="refresh-button" bindtap="refreshCurrentFeeds">
      <image class="refresh-icon" src="../../images/refresh.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 文章列表 -->
  <view class="article-list">
    <!-- 加载提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载订阅源...</text>
      <text class="loading-subtext">首次加载可能需要较长时间，请耐心等待</text>
    </view>

    <!-- 无内容提示 -->
    <view class="empty-container" wx:if="{{!loading && articles.length === 0}}">
      <text class="empty-text">暂无内容</text>
      <text class="empty-subtext">请添加订阅源或点击刷新按钮</text>
    </view>

    <block wx:for="{{articles}}" wx:key="guid">
      <view class="article-card" bindtap="goToDetail" data-article="{{item}}">
        <view class="article-header">
          <view class="article-title">{{item.title}}</view>
          <view class="action-button favorite" catchtap="handleToggleFavorite" data-guid="{{item.guid}}" data-index="{{index}}">
            <image class="favorite-icon" src="../../images/{{item.isFavorite === true ? 'star-active.png' : 'star.png'}}"></image>
          </view>
        </view>
        <view class="article-meta">
          <text class="meta-author">作者：{{item.author}}</text>
          <text class="meta-dot">·</text>
          <text class="meta-date">{{item.pubDate}}</text>
          <text class="meta-dot">·</text>
          <text class="meta-source">来源：{{item.source}}</text>
        </view>
        <view class="article-summary">{{item.summary}}</view>
      </view>
    </block>
  </view>
</view>