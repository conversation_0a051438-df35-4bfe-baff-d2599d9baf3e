const { getAllFavorites } = require('../../utils/favorite-manager')
const { formatDate } = require('../../utils/rss-parser')

Page({
  data: {
    favorites: []
  },
  onShow() {
    this.loadFavorites()
  },
  loadFavorites() {
    const favorites = getAllFavorites().map(article => ({
      ...article,
      pubDate: article.pubDate ? formatDate(article.pubDate) : ''
    }))
    this.setData({ favorites })
  },
  goToDetail(e) {
    const article = e.currentTarget.dataset.article
    wx.navigateTo({
      url: `/pages/detail/detail?article=${encodeURIComponent(JSON.stringify(article))}`
    })
  }
}) 