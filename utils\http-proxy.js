// HTTP代理工具
// 统一通过代理服务器发送RSS请求

const PROXY_URL = 'https://www.apiworker.fun';

/**
 * 通过代理发送RSS请求
 * @param {string} rssUrl - 需要代理的RSS URL
 * @param {object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export function requestRSSViaProxy(rssUrl, options = {}) {
  return new Promise(function(resolve, reject) {
    // 构建请求参数
    const requestOptions = {
      url: PROXY_URL,
      method: 'GET',
      header: {
        'rss_url': rssUrl
      },
      timeout: options.timeout || 10000, // 默认15秒超时
      success: function(res) {
        if (res.statusCode === 200) {
          resolve(res);
        } else {
          reject(new Error(`代理请求状态码异常: ${res.statusCode}`));
        }
      },
      fail: function(err) {
        reject(err);
      }
    };

    // 合并自定义选项
    if (options.header) {
      requestOptions.header = {
        ...requestOptions.header,
        ...options.header
      };
    }

    // 发送请求
    wx.request(requestOptions);
  });
}

/**
 * 验证RSS源是否有效（通过代理）
 * @param {string} rssUrl - RSS URL
 * @returns {Promise} - 返回Promise对象
 */
export function validateRSSViaProxy(rssUrl) {
  return new Promise(function(resolve, reject) {
    requestRSSViaProxy(rssUrl, {
      timeout: 10000 // 验证时使用较短的超时时间
    }).then(function(response) {
      // 检查响应状态码
      if (response.statusCode !== 200) {
        throw new Error(`Invalid response status: ${response.statusCode}`);
      }

      // 检查响应内容是否为有效的RSS/Atom格式
      const responseData = response.data;
      if (typeof responseData === 'string') {
        const isXml = responseData.includes('<?xml') ||
                      responseData.includes('<rss') ||
                      responseData.includes('<feed');
        
        if (!isXml) {
          throw new Error('无效的RSS格式');
        }
      } else if (typeof responseData === 'object') {
        if (responseData.error) {
          throw new Error(responseData.error);
        }
        // 检查JSON响应是否包含必要的RSS结构
        if (!responseData.items && !responseData.feed) {
          throw new Error('无效的RSS数据结构');
        }
      }

      resolve(response);
    }).catch(function(err) {
      reject(err);
    });
  });
}

/**
 * 批量通过代理请求多个RSS源
 * @param {Array} rssUrls - RSS URL数组
 * @param {object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
export function batchRequestRSSViaProxy(rssUrls, options = {}) {
  const batchSize = options.batchSize || 3; // 默认每批3个
  const promises = [];

  // 将URLs分批处理
  for (let i = 0; i < rssUrls.length; i += batchSize) {
    const batch = rssUrls.slice(i, i + batchSize);
    const batchPromises = batch.map(function(url) {
      return requestRSSViaProxy(url, options).catch(function(err) {
        return { error: err, url: url };
      });
    });
    promises.push(Promise.all(batchPromises));
  }

  return Promise.all(promises).then(function(batchResults) {
    // 展平结果数组
    return batchResults.flat();
  });
}

/**
 * 获取代理服务器状态
 * @returns {Promise} - 返回Promise对象
 */
export function checkProxyStatus() {
  return new Promise(function(resolve, reject) {
    wx.request({
      url: PROXY_URL,
      method: 'GET',
      header: {
        'rss_url': 'https://rss.keepdev.fun/rss/bilibili/user/dynamic/289706107' // 使用有效的RSS URL作为测试
      },
      timeout: 5000,
      success: function(res) {
        if (res.statusCode === 200) {
          resolve({ status: 'ok', message: '代理服务器正常' });
        } else {
          resolve({ status: 'error', message: `代理服务器响应异常: ${res.statusCode}` });
        }
      },
      fail: function(err) {
        reject({ status: 'error', message: '无法连接到代理服务器' });
      }
    });
  });
}
