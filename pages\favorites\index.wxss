.container {
  padding: 0;
  min-height: 100vh;
  background: #f7f7f7;
}
.page-title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 32rpx 24rpx 0 24rpx;
  color: #222;
}
.article-list {
  padding: 16rpx;
}
.article-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx;
  transition: box-shadow 0.2s;
}
.article-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  color: #222;
  line-height: 1.3;
}
.article-meta {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 10rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.meta-dot {
  margin: 0 8rpx;
}
.article-summary {
  color: #444;
  font-size: 26rpx;
  margin-top: 6rpx;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
} 